pub mod connector;
pub mod reader;

pub use connector::MemoryConnector;
pub use reader::MemoryReader;

use anyhow::Result;
use memflow::prelude::*;

/// 内存访问接口
#[async_trait::async_trait]
pub trait MemoryAccess {
    /// 读取指定地址的数据
    async fn read_bytes(&self, address: u64, size: usize) -> Result<Vec<u8>>;
    
    /// 读取指定类型的数据
    async fn read<T: Pod>(&self, address: u64) -> Result<T>;
    
    /// 批量读取多个地址的数据
    async fn read_batch(&self, addresses: &[(u64, usize)]) -> Result<Vec<Vec<u8>>>;
    
    /// 检查地址是否可读
    async fn is_readable(&self, address: u64, size: usize) -> bool;
}

/// 进程信息
#[derive(Debug, Clone)]
pub struct ProcessInfo {
    pub pid: u32,
    pub name: String,
    pub base_address: u64,
    pub memory_size: u64,
}

/// 内存区域信息
#[derive(Debug, Clone)]
pub struct MemoryRegion {
    pub start_address: u64,
    pub end_address: u64,
    pub size: u64,
    pub permissions: String,
    pub name: String,
}

impl ProcessInfo {
    pub fn new(pid: u32, name: String, base_address: u64, memory_size: u64) -> Self {
        Self {
            pid,
            name,
            base_address,
            memory_size,
        }
    }
    
    pub fn is_valid(&self) -> bool {
        self.pid > 0 && self.base_address > 0
    }
}
