pub mod radar;

pub use radar::RadarUI;

use eframe::egui;
use crate::game::{PlayerEntity, Vector3, Team};

/// UI主题配置
pub struct UITheme {
    pub background_color: egui::Color32,
    pub text_color: egui::Color32,
    pub accent_color: egui::Color32,
    pub enemy_color: egui::Color32,
    pub teammate_color: egui::Color32,
    pub local_player_color: egui::Color32,
}

impl Default for UITheme {
    fn default() -> Self {
        Self {
            background_color: egui::Color32::from_rgb(20, 20, 30),
            text_color: egui::Color32::WHITE,
            accent_color: egui::Color32::from_rgb(100, 150, 255),
            enemy_color: egui::Color32::from_rgb(255, 100, 100),
            teammate_color: egui::Color32::from_rgb(100, 255, 100),
            local_player_color: egui::Color32::from_rgb(255, 255, 100),
        }
    }
}

/// UI工具函数
pub struct UIUtils;

impl UIUtils {
    /// 绘制玩家信息面板
    pub fn draw_player_info_panel(
        ui: &mut egui::Ui,
        player: &PlayerEntity,
        theme: &UITheme,
    ) {
        ui.group(|ui| {
            ui.horizontal(|ui| {
                // 玩家名称
                ui.label(egui::RichText::new(&player.name).color(theme.text_color));
                
                ui.separator();
                
                // 血量
                let health_color = if player.health > 70 {
                    egui::Color32::GREEN
                } else if player.health > 30 {
                    egui::Color32::YELLOW
                } else {
                    egui::Color32::RED
                };
                
                ui.label(egui::RichText::new(format!("HP: {}", player.health)).color(health_color));
                
                ui.separator();
                
                // 队伍
                let team_text = match player.team {
                    Team::Terrorist => "T",
                    Team::CounterTerrorist => "CT",
                    _ => "?",
                };
                
                let team_color = match player.team {
                    Team::Terrorist => theme.enemy_color,
                    Team::CounterTerrorist => theme.teammate_color,
                    _ => theme.text_color,
                };
                
                ui.label(egui::RichText::new(team_text).color(team_color));
            });
            
            // 位置信息
            ui.label(format!(
                "位置: ({:.1}, {:.1}, {:.1})",
                player.position.x, player.position.y, player.position.z
            ));
            
            // 其他信息
            ui.horizontal(|ui| {
                ui.label(format!("击杀: {}", player.kills));
                ui.separator();
                ui.label(format!("死亡: {}", player.deaths));
                ui.separator();
                ui.label(format!("助攻: {}", player.assists));
            });
        });
    }
    
    /// 绘制性能统计
    pub fn draw_performance_stats(
        ui: &mut egui::Ui,
        fps: f32,
        memory_usage: u64,
        update_time: std::time::Duration,
    ) {
        ui.group(|ui| {
            ui.label("性能统计");
            ui.separator();
            
            ui.horizontal(|ui| {
                ui.label(format!("FPS: {:.1}", fps));
                ui.separator();
                ui.label(format!("内存: {:.1} MB", memory_usage as f64 / 1024.0 / 1024.0));
                ui.separator();
                ui.label(format!("更新时间: {:.1} ms", update_time.as_millis()));
            });
        });
    }
    
    /// 绘制配置面板
    pub fn draw_config_panel(
        ui: &mut egui::Ui,
        config: &mut super::RadarConfig,
    ) {
        ui.group(|ui| {
            ui.label("雷达配置");
            ui.separator();
            
            ui.horizontal(|ui| {
                ui.label("缩放:");
                ui.add(egui::Slider::new(&mut config.scale, 0.1..=5.0).step_by(0.1));
            });
            
            ui.horizontal(|ui| {
                ui.label("最大距离:");
                ui.add(egui::Slider::new(&mut config.max_distance, 100.0..=5000.0).step_by(100.0));
            });
            
            ui.checkbox(&mut config.show_teammates, "显示队友");
            ui.checkbox(&mut config.show_enemies, "显示敌人");
        });
    }
    
    /// 世界坐标转屏幕坐标
    pub fn world_to_screen(
        world_pos: Vector3,
        reference_pos: Vector3,
        screen_center: egui::Pos2,
        scale: f32,
        max_distance: f32,
        radar_radius: f32,
    ) -> Option<egui::Pos2> {
        let relative_pos = Vector3::new(
            world_pos.x - reference_pos.x,
            world_pos.y - reference_pos.y,
            world_pos.z - reference_pos.z,
        );
        
        let distance = relative_pos.distance_2d_to(&Vector3::zero());
        if distance > max_distance {
            return None;
        }
        
        let screen_x = screen_center.x + (relative_pos.x * scale * radar_radius / max_distance);
        let screen_y = screen_center.y - (relative_pos.y * scale * radar_radius / max_distance);
        
        Some(egui::Pos2::new(screen_x, screen_y))
    }
    
    /// 绘制方向指示器
    pub fn draw_direction_indicator(
        painter: &egui::Painter,
        center: egui::Pos2,
        angle: f32,
        radius: f32,
        color: egui::Color32,
    ) {
        let end_x = center.x + angle.cos() * radius;
        let end_y = center.y + angle.sin() * radius;
        let end_pos = egui::Pos2::new(end_x, end_y);
        
        painter.line_segment(
            [center, end_pos],
            egui::Stroke::new(2.0, color),
        );
        
        // 绘制箭头
        let arrow_size = 5.0;
        let arrow_angle1 = angle + std::f32::consts::PI * 0.8;
        let arrow_angle2 = angle - std::f32::consts::PI * 0.8;
        
        let arrow1 = egui::Pos2::new(
            end_x + arrow_angle1.cos() * arrow_size,
            end_y + arrow_angle1.sin() * arrow_size,
        );
        
        let arrow2 = egui::Pos2::new(
            end_x + arrow_angle2.cos() * arrow_size,
            end_y + arrow_angle2.sin() * arrow_size,
        );
        
        painter.line_segment([end_pos, arrow1], egui::Stroke::new(2.0, color));
        painter.line_segment([end_pos, arrow2], egui::Stroke::new(2.0, color));
    }
}
