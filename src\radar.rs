use anyhow::Result;
use eframe::egui;
use log::{info, error};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};

mod ui;
use ui::RadarUI;

use crate::game::{PlayerEntity, Vector3, Team};

/// 雷达应用程序
pub struct RadarApp {
    /// 雷达UI
    radar_ui: RadarUI,
    /// 玩家数据
    players: Arc<Mutex<Vec<PlayerEntity>>>,
    /// 上次更新时间
    last_update: Instant,
    /// 更新间隔
    update_interval: Duration,
    /// 应用配置
    config: RadarConfig,
}

/// 雷达配置
#[derive(Debug, Clone)]
pub struct RadarConfig {
    /// 雷达缩放比例
    pub scale: f32,
    /// 雷达中心位置
    pub center: Vector3,
    /// 是否显示队友
    pub show_teammates: bool,
    /// 是否显示敌人
    pub show_enemies: bool,
    /// 最大显示距离
    pub max_distance: f32,
    /// 窗口大小
    pub window_size: (f32, f32),
}

impl Default for RadarConfig {
    fn default() -> Self {
        Self {
            scale: 1.0,
            center: Vector3::zero(),
            show_teammates: true,
            show_enemies: true,
            max_distance: 2000.0,
            window_size: (800.0, 600.0),
        }
    }
}

impl RadarApp {
    pub fn new(cc: &eframe::CreationContext<'_>) -> Self {
        // 配置字体
        let mut fonts = egui::FontDefinitions::default();
        fonts.font_data.insert(
            "my_font".to_owned(),
            egui::FontData::from_static(include_bytes!("../assets/fonts/NotoSansCJK-Regular.ttc")),
        );
        fonts
            .families
            .entry(egui::FontFamily::Proportional)
            .or_default()
            .insert(0, "my_font".to_owned());
        cc.egui_ctx.set_fonts(fonts);

        Self {
            radar_ui: RadarUI::new(),
            players: Arc::new(Mutex::new(Vec::new())),
            last_update: Instant::now(),
            update_interval: Duration::from_millis(16), // ~60 FPS
            config: RadarConfig::default(),
        }
    }
    
    /// 更新玩家数据
    pub fn update_players(&mut self, new_players: Vec<PlayerEntity>) {
        if let Ok(mut players) = self.players.lock() {
            *players = new_players;
            self.last_update = Instant::now();
        }
    }
    
    /// 从共享内存读取玩家数据
    async fn read_shared_memory(&mut self) -> Result<()> {
        // 这里实现从共享内存读取玩家数据的逻辑
        // 与主程序通信获取最新的玩家信息
        Ok(())
    }
}

impl eframe::App for RadarApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // 定期更新数据
        if self.last_update.elapsed() >= self.update_interval {
            // 尝试从共享内存读取数据
            tokio::spawn(async move {
                // 异步读取数据
            });
        }
        
        // 主面板
        egui::CentralPanel::default().show(ctx, |ui| {
            ui.heading("CS2 Virtual DMA - 雷达显示");
            
            // 配置面板
            ui.horizontal(|ui| {
                ui.label("缩放:");
                ui.add(egui::Slider::new(&mut self.config.scale, 0.1..=5.0));
                
                ui.separator();
                
                ui.checkbox(&mut self.config.show_teammates, "显示队友");
                ui.checkbox(&mut self.config.show_enemies, "显示敌人");
                
                ui.separator();
                
                ui.label("最大距离:");
                ui.add(egui::Slider::new(&mut self.config.max_distance, 100.0..=5000.0));
            });
            
            ui.separator();
            
            // 雷达显示区域
            let available_size = ui.available_size();
            let radar_size = egui::Vec2::new(
                available_size.x.min(600.0),
                available_size.y.min(600.0),
            );
            
            let (response, painter) = ui.allocate_painter(radar_size, egui::Sense::hover());
            let radar_rect = response.rect;
            
            // 绘制雷达背景
            painter.rect_filled(
                radar_rect,
                egui::Rounding::same(5.0),
                egui::Color32::from_rgb(20, 20, 30),
            );
            
            // 绘制雷达网格
            self.draw_radar_grid(&painter, radar_rect);
            
            // 绘制玩家
            if let Ok(players) = self.players.lock() {
                self.draw_players(&painter, radar_rect, &players);
            }
            
            // 状态信息
            ui.separator();
            ui.horizontal(|ui| {
                ui.label(format!("更新时间: {:?}", self.last_update.elapsed()));
                
                if let Ok(players) = self.players.lock() {
                    ui.label(format!("玩家数量: {}", players.len()));
                    
                    let valid_players = players.iter().filter(|p| p.is_valid()).count();
                    ui.label(format!("有效玩家: {}", valid_players));
                }
            });
        });
        
        // 请求重绘
        ctx.request_repaint_after(Duration::from_millis(16));
    }
}

impl RadarApp {
    /// 绘制雷达网格
    fn draw_radar_grid(&self, painter: &egui::Painter, rect: egui::Rect) {
        let center = rect.center();
        let grid_color = egui::Color32::from_rgb(40, 40, 50);
        
        // 绘制十字线
        painter.line_segment(
            [egui::Pos2::new(rect.left(), center.y), egui::Pos2::new(rect.right(), center.y)],
            egui::Stroke::new(1.0, grid_color),
        );
        painter.line_segment(
            [egui::Pos2::new(center.x, rect.top()), egui::Pos2::new(center.x, rect.bottom())],
            egui::Stroke::new(1.0, grid_color),
        );
        
        // 绘制同心圆
        let max_radius = rect.width().min(rect.height()) / 2.0;
        for i in 1..=4 {
            let radius = max_radius * i as f32 / 4.0;
            painter.circle_stroke(
                center,
                radius,
                egui::Stroke::new(1.0, grid_color),
            );
        }
    }
    
    /// 绘制玩家
    fn draw_players(&self, painter: &egui::Painter, rect: egui::Rect, players: &[PlayerEntity]) {
        let center = rect.center();
        let max_radius = rect.width().min(rect.height()) / 2.0;
        
        // 找到本地玩家作为参考点
        let local_player = players.iter().find(|p| p.index == 1); // 假设索引1是本地玩家
        let reference_pos = local_player.map(|p| p.position).unwrap_or(Vector3::zero());
        
        for player in players {
            if !player.is_valid() {
                continue;
            }
            
            // 计算相对位置
            let relative_pos = Vector3::new(
                player.position.x - reference_pos.x,
                player.position.y - reference_pos.y,
                player.position.z - reference_pos.z,
            );
            
            // 检查距离
            let distance = relative_pos.distance_2d_to(&Vector3::zero());
            if distance > self.config.max_distance {
                continue;
            }
            
            // 转换为屏幕坐标
            let screen_x = center.x + (relative_pos.x * self.config.scale * max_radius / self.config.max_distance);
            let screen_y = center.y - (relative_pos.y * self.config.scale * max_radius / self.config.max_distance);
            let screen_pos = egui::Pos2::new(screen_x, screen_y);
            
            // 确保在雷达范围内
            if !rect.contains(screen_pos) {
                continue;
            }
            
            // 根据队伍选择颜色
            let color = match player.team {
                Team::Terrorist => egui::Color32::from_rgb(255, 100, 100), // 红色
                Team::CounterTerrorist => egui::Color32::from_rgb(100, 100, 255), // 蓝色
                _ => egui::Color32::GRAY,
            };
            
            // 绘制玩家点
            let radius = if Some(player) == local_player { 8.0 } else { 6.0 };
            painter.circle_filled(screen_pos, radius, color);
            
            // 绘制血量条
            if player.health > 0 {
                let health_ratio = player.health as f32 / 100.0;
                let bar_width = 20.0;
                let bar_height = 3.0;
                let bar_pos = egui::Pos2::new(screen_x - bar_width / 2.0, screen_y - radius - 8.0);
                
                // 背景
                painter.rect_filled(
                    egui::Rect::from_min_size(bar_pos, egui::Vec2::new(bar_width, bar_height)),
                    egui::Rounding::same(1.0),
                    egui::Color32::from_rgb(50, 50, 50),
                );
                
                // 血量
                let health_color = if health_ratio > 0.6 {
                    egui::Color32::GREEN
                } else if health_ratio > 0.3 {
                    egui::Color32::YELLOW
                } else {
                    egui::Color32::RED
                };
                
                painter.rect_filled(
                    egui::Rect::from_min_size(bar_pos, egui::Vec2::new(bar_width * health_ratio, bar_height)),
                    egui::Rounding::same(1.0),
                    health_color,
                );
            }
            
            // 绘制玩家名称
            if !player.name.is_empty() {
                painter.text(
                    egui::Pos2::new(screen_x, screen_y + radius + 5.0),
                    egui::Align2::CENTER_TOP,
                    &player.name,
                    egui::FontId::default(),
                    egui::Color32::WHITE,
                );
            }
        }
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::init();
    
    info!("启动CS2雷达界面...");
    
    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([800.0, 600.0])
            .with_title("CS2 Virtual DMA - 雷达"),
        ..Default::default()
    };
    
    eframe::run_native(
        "CS2 Radar",
        options,
        Box::new(|cc| Box::new(RadarApp::new(cc))),
    ).map_err(|e| anyhow::anyhow!("GUI错误: {}", e))?;
    
    Ok(())
}
