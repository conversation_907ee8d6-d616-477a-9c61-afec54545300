use anyhow::{Result, Context};
use log::{debug, warn};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

use crate::memory::{MemoryConnector, MemoryReader, ProcessInfo};
use super::offsets::CS2Offsets;

/// 3D向量
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Vector3 {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

impl Vector3 {
    pub fn new(x: f32, y: f32, z: f32) -> Self {
        Self { x, y, z }
    }
    
    pub fn zero() -> Self {
        Self::new(0.0, 0.0, 0.0)
    }
    
    pub fn distance_to(&self, other: &Vector3) -> f32 {
        let dx = self.x - other.x;
        let dy = self.y - other.y;
        let dz = self.z - other.z;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }
    
    pub fn distance_2d_to(&self, other: &Vector3) -> f32 {
        let dx = self.x - other.x;
        let dy = self.y - other.y;
        (dx * dx + dy * dy).sqrt()
    }
}

/// 玩家队伍
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum Team {
    None = 0,
    Spectator = 1,
    Terrorist = 2,
    CounterTerrorist = 3,
}

impl From<u32> for Team {
    fn from(value: u32) -> Self {
        match value {
            1 => Team::Spectator,
            2 => Team::Terrorist,
            3 => Team::CounterTerrorist,
            _ => Team::None,
        }
    }
}

/// 玩家实体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlayerEntity {
    /// 实体索引
    pub index: u32,
    /// 玩家名称
    pub name: String,
    /// 位置
    pub position: Vector3,
    /// 视角角度
    pub view_angles: Vector3,
    /// 血量
    pub health: i32,
    /// 护甲
    pub armor: i32,
    /// 队伍
    pub team: Team,
    /// 是否存活
    pub is_alive: bool,
    /// 是否在地面
    pub is_on_ground: bool,
    /// 速度
    pub velocity: Vector3,
    /// 武器ID
    pub weapon_id: u32,
    /// 金钱
    pub money: i32,
    /// 击杀数
    pub kills: i32,
    /// 死亡数
    pub deaths: i32,
    /// 助攻数
    pub assists: i32,
}

impl PlayerEntity {
    pub fn new(index: u32) -> Self {
        Self {
            index,
            name: String::new(),
            position: Vector3::zero(),
            view_angles: Vector3::zero(),
            health: 0,
            armor: 0,
            team: Team::None,
            is_alive: false,
            is_on_ground: false,
            velocity: Vector3::zero(),
            weapon_id: 0,
            money: 0,
            kills: 0,
            deaths: 0,
            assists: 0,
        }
    }
    
    /// 检查实体是否有效
    pub fn is_valid(&self) -> bool {
        self.health > 0 && 
        self.team != Team::None && 
        self.team != Team::Spectator &&
        self.is_alive
    }
    
    /// 检查是否为敌人
    pub fn is_enemy(&self, my_team: Team) -> bool {
        self.is_valid() && 
        self.team != my_team && 
        self.team != Team::None && 
        self.team != Team::Spectator
    }
    
    /// 检查是否为队友
    pub fn is_teammate(&self, my_team: Team) -> bool {
        self.is_valid() && 
        self.team == my_team
    }
}

/// 实体管理器
pub struct EntityManager {
    /// 进程信息
    process_info: ProcessInfo,
    /// 内存读取器
    memory_reader: MemoryReader,
    /// CS2偏移量
    offsets: CS2Offsets,
    /// 玩家实体缓存
    entities: HashMap<u32, PlayerEntity>,
    /// 本地玩家索引
    local_player_index: Option<u32>,
}

impl EntityManager {
    pub fn new(process_info: ProcessInfo) -> Self {
        // 这里需要传入MemoryConnector来创建MemoryReader
        // 为了简化，我们先创建一个占位符
        todo!("需要重构以接受MemoryConnector参数")
    }
    
    /// 使用MemoryConnector创建EntityManager
    pub fn with_connector(process_info: ProcessInfo, connector: std::sync::Arc<MemoryConnector>) -> Self {
        let memory_reader = MemoryReader::new(connector);
        
        Self {
            process_info,
            memory_reader,
            offsets: CS2Offsets::new(),
            entities: HashMap::new(),
            local_player_index: None,
        }
    }
    
    /// 更新所有实体
    pub async fn update_entities(&mut self, connector: &MemoryConnector) -> Result<Vec<PlayerEntity>> {
        // 获取客户端基址
        let client_base = self.get_client_base(connector).await?;
        
        // 更新本地玩家
        self.update_local_player(client_base).await?;
        
        // 更新所有玩家实体
        self.update_player_entities(client_base).await?;
        
        // 返回有效实体列表
        Ok(self.entities.values().cloned().collect())
    }
    
    /// 获取客户端模块基址
    async fn get_client_base(&self, connector: &MemoryConnector) -> Result<u64> {
        // 这里需要从GameProcess获取客户端基址
        // 为了简化，我们使用一个固定的基址
        // 在实际实现中，应该从模块列表中获取
        Ok(0x140000000) // 占位符基址
    }
    
    /// 更新本地玩家信息
    async fn update_local_player(&mut self, client_base: u64) -> Result<()> {
        let local_player_ptr = client_base + self.offsets.dwLocalPlayerPawn;
        let local_player_addr = self.memory_reader.read_u64(local_player_ptr).await?;
        
        if local_player_addr == 0 {
            return Ok(());
        }
        
        // 读取本地玩家索引
        let index_addr = local_player_addr + self.offsets.m_iIDEntIndex;
        let local_index = self.memory_reader.read_u32(index_addr).await?;
        
        self.local_player_index = Some(local_index);
        debug!("本地玩家索引: {}", local_index);
        
        Ok(())
    }
    
    /// 更新玩家实体
    async fn update_player_entities(&mut self, client_base: u64) -> Result<()> {
        let entity_list_ptr = client_base + self.offsets.dwEntityList;
        
        // 清空旧的实体数据
        self.entities.clear();
        
        // 遍历实体列表
        for i in 1..65 { // CS2最多64个玩家
            match self.read_player_entity(entity_list_ptr, i).await {
                Ok(Some(entity)) => {
                    if entity.is_valid() {
                        self.entities.insert(i, entity);
                    }
                }
                Ok(None) => continue,
                Err(e) => {
                    debug!("读取实体 {} 失败: {}", i, e);
                    continue;
                }
            }
        }
        
        debug!("更新了 {} 个有效实体", self.entities.len());
        Ok(())
    }
    
    /// 读取单个玩家实体
    async fn read_player_entity(&self, entity_list_ptr: u64, index: u32) -> Result<Option<PlayerEntity>> {
        // 计算实体地址
        let entity_entry_addr = entity_list_ptr + (index as u64 * 0x78);
        let entity_ptr = self.memory_reader.read_u64(entity_entry_addr).await?;
        
        if entity_ptr == 0 {
            return Ok(None);
        }
        
        let mut entity = PlayerEntity::new(index);
        
        // 读取基本信息
        entity.health = self.memory_reader.read_i32(entity_ptr + self.offsets.m_iHealth).await.unwrap_or(0);
        entity.armor = self.memory_reader.read_i32(entity_ptr + self.offsets.m_ArmorValue).await.unwrap_or(0);
        
        let team_num = self.memory_reader.read_u32(entity_ptr + self.offsets.m_iTeamNum).await.unwrap_or(0);
        entity.team = Team::from(team_num);
        
        // 读取位置
        let pos_x = self.memory_reader.read_f32(entity_ptr + self.offsets.m_vOldOrigin).await.unwrap_or(0.0);
        let pos_y = self.memory_reader.read_f32(entity_ptr + self.offsets.m_vOldOrigin + 4).await.unwrap_or(0.0);
        let pos_z = self.memory_reader.read_f32(entity_ptr + self.offsets.m_vOldOrigin + 8).await.unwrap_or(0.0);
        entity.position = Vector3::new(pos_x, pos_y, pos_z);
        
        // 读取生命状态
        let life_state = self.memory_reader.read_u8(entity_ptr + self.offsets.m_lifeState).await.unwrap_or(1);
        entity.is_alive = life_state == 0;
        
        // 读取玩家名称（如果可能）
        if let Ok(name) = self.read_player_name(entity_ptr).await {
            entity.name = name;
        }
        
        Ok(Some(entity))
    }
    
    /// 读取玩家名称
    async fn read_player_name(&self, entity_ptr: u64) -> Result<String> {
        // 这是一个简化的实现
        // 实际的玩家名称读取可能需要更复杂的指针链
        let name_ptr = self.memory_reader.read_u64(entity_ptr + self.offsets.m_iszPlayerName).await?;
        if name_ptr == 0 {
            return Ok(format!("Player_{}", rand::random::<u16>()));
        }
        
        self.memory_reader.read_string(name_ptr, 64).await
            .unwrap_or_else(|_| format!("Player_{}", rand::random::<u16>()))
            .into()
    }
    
    /// 获取本地玩家
    pub fn get_local_player(&self) -> Option<&PlayerEntity> {
        if let Some(index) = self.local_player_index {
            self.entities.get(&index)
        } else {
            None
        }
    }
    
    /// 获取所有敌人
    pub fn get_enemies(&self) -> Vec<&PlayerEntity> {
        if let Some(local_player) = self.get_local_player() {
            self.entities.values()
                .filter(|entity| entity.is_enemy(local_player.team))
                .collect()
        } else {
            Vec::new()
        }
    }
    
    /// 获取所有队友
    pub fn get_teammates(&self) -> Vec<&PlayerEntity> {
        if let Some(local_player) = self.get_local_player() {
            self.entities.values()
                .filter(|entity| entity.is_teammate(local_player.team))
                .collect()
        } else {
            Vec::new()
        }
    }
}
