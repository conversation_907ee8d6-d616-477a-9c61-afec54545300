use anyhow::{Result, Context};
use log::{info, warn, error, debug};
use memflow::prelude::*;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

use super::{MemoryAccess, ProcessInfo, MemoryRegion};

/// MemFlow-QEMU内存连接器
pub struct MemoryConnector {
    /// 物理内存连接器
    physical_memory: Arc<RwLock<Box<dyn PhysicalMemory>>>,
    /// 虚拟内存连接器
    virtual_memory: Arc<RwLock<Option<VirtualMemory<Box<dyn PhysicalMemory>>>>>,
    /// 进程列表缓存
    process_cache: Arc<RwLock<HashMap<u32, ProcessInfo>>>,
    /// 当前附加的进程
    current_process: Arc<RwLock<Option<ProcessInfo>>>,
}

impl MemoryConnector {
    /// 创建新的内存连接器
    pub async fn new() -> Result<Self> {
        info!("初始化MemFlow-QEMU连接器...");
        
        // 初始化MemFlow库
        let inventory = Inventory::scan();
        
        // 查找QEMU连接器
        let qemu_connector = inventory
            .builder()
            .connector("qemu")
            .context("未找到QEMU连接器")?;
            
        // 创建物理内存连接器
        let physical_memory = qemu_connector
            .build()
            .context("创建QEMU连接器失败")?;
            
        info!("QEMU连接器创建成功");
        
        Ok(Self {
            physical_memory: Arc::new(RwLock::new(physical_memory)),
            virtual_memory: Arc::new(RwLock::new(None)),
            process_cache: Arc::new(RwLock::new(HashMap::new())),
            current_process: Arc::new(RwLock::new(None)),
        })
    }
    
    /// 扫描虚拟机中的进程
    pub async fn scan_processes(&self) -> Result<Vec<ProcessInfo>> {
        debug!("扫描虚拟机进程...");
        
        let physical_memory = self.physical_memory.read().await;
        
        // 创建Windows内核连接器
        let mut kernel = memflow_win32::Kernel::builder(physical_memory.clone())
            .build_default_caches()
            .build()
            .context("创建Windows内核连接器失败")?;
            
        // 获取进程列表
        let process_list = kernel.process_info_list()
            .context("获取进程列表失败")?;
            
        let mut processes = Vec::new();
        let mut cache = self.process_cache.write().await;
        cache.clear();
        
        for process_info in process_list {
            let process = ProcessInfo::new(
                process_info.pid,
                process_info.name.clone(),
                process_info.dtb.as_u64(),
                0, // 稍后获取内存大小
            );
            
            cache.insert(process_info.pid, process.clone());
            processes.push(process);
            
            debug!("发现进程: {} (PID: {})", process_info.name, process_info.pid);
        }
        
        info!("扫描完成，发现 {} 个进程", processes.len());
        Ok(processes)
    }
    
    /// 附加到指定进程
    pub async fn attach_to_process(&self, pid: u32) -> Result<ProcessInfo> {
        info!("附加到进程 PID: {}", pid);
        
        // 从缓存中查找进程信息
        let cache = self.process_cache.read().await;
        let process_info = cache.get(&pid)
            .cloned()
            .context("进程不存在")?;
        drop(cache);
        
        // 创建虚拟内存连接器
        let physical_memory = self.physical_memory.read().await;
        let mut kernel = memflow_win32::Kernel::builder(physical_memory.clone())
            .build_default_caches()
            .build()
            .context("创建Windows内核连接器失败")?;
            
        let process = kernel.process_from_pid(pid)
            .context("无法获取进程对象")?;
            
        let virtual_memory = VirtualMemory::new(
            physical_memory.clone(),
            process.proc_arch,
            process.dtb,
        );
        
        // 更新虚拟内存连接器
        let mut vm_lock = self.virtual_memory.write().await;
        *vm_lock = Some(virtual_memory);
        drop(vm_lock);
        
        // 更新当前进程
        let mut current = self.current_process.write().await;
        *current = Some(process_info.clone());
        drop(current);
        
        info!("成功附加到进程: {} (PID: {})", process_info.name, process_info.pid);
        Ok(process_info)
    }
    
    /// 获取进程内存区域
    pub async fn get_memory_regions(&self) -> Result<Vec<MemoryRegion>> {
        let current_process = self.current_process.read().await;
        let process_info = current_process.as_ref()
            .context("未附加到任何进程")?;
        drop(current_process);
        
        let physical_memory = self.physical_memory.read().await;
        let mut kernel = memflow_win32::Kernel::builder(physical_memory.clone())
            .build_default_caches()
            .build()
            .context("创建Windows内核连接器失败")?;
            
        let process = kernel.process_from_pid(process_info.pid)
            .context("无法获取进程对象")?;
            
        let module_list = process.module_list()
            .context("获取模块列表失败")?;
            
        let mut regions = Vec::new();
        
        for module in module_list {
            let region = MemoryRegion {
                start_address: module.base.as_u64(),
                end_address: module.base.as_u64() + module.size as u64,
                size: module.size as u64,
                permissions: "r-x".to_string(), // 简化的权限表示
                name: module.name.clone(),
            };
            regions.push(region);
        }
        
        debug!("获取到 {} 个内存区域", regions.len());
        Ok(regions)
    }
    
    /// 查找CS2游戏进程
    pub async fn find_cs2_process(&self) -> Result<ProcessInfo> {
        let processes = self.scan_processes().await?;
        
        // 查找CS2相关进程
        let cs2_names = ["cs2.exe", "csgo.exe", "Counter-Strike 2.exe"];
        
        for process in processes {
            for &cs2_name in &cs2_names {
                if process.name.to_lowercase().contains(&cs2_name.to_lowercase()) {
                    info!("找到CS2进程: {} (PID: {})", process.name, process.pid);
                    return Ok(process);
                }
            }
        }
        
        Err(anyhow::anyhow!("未找到CS2进程"))
    }
}

#[async_trait::async_trait]
impl MemoryAccess for MemoryConnector {
    async fn read_bytes(&self, address: u64, size: usize) -> Result<Vec<u8>> {
        let vm_lock = self.virtual_memory.read().await;
        let virtual_memory = vm_lock.as_ref()
            .context("未附加到任何进程")?;
            
        let mut buffer = vec![0u8; size];
        virtual_memory.read_raw_into(address.into(), &mut buffer)
            .context("读取内存失败")?;
            
        Ok(buffer)
    }
    
    async fn read<T: Pod>(&self, address: u64) -> Result<T> {
        let bytes = self.read_bytes(address, std::mem::size_of::<T>()).await?;
        Ok(T::from_byte_slice(&bytes)?)
    }
    
    async fn read_batch(&self, addresses: &[(u64, usize)]) -> Result<Vec<Vec<u8>>> {
        let mut results = Vec::new();
        
        for &(address, size) in addresses {
            match self.read_bytes(address, size).await {
                Ok(data) => results.push(data),
                Err(_) => results.push(vec![0u8; size]), // 失败时返回零填充
            }
        }
        
        Ok(results)
    }
    
    async fn is_readable(&self, address: u64, size: usize) -> bool {
        self.read_bytes(address, size).await.is_ok()
    }
}
