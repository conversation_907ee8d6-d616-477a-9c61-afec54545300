[package]
name = "cs2-virtual-dma"
version = "0.1.0"
edition = "2021"
authors = ["Red Team <<EMAIL>>"]
description = "Virtual DMA architecture for CS2 anti-cheat testing"

[dependencies]
# MemFlow core dependencies
memflow = "0.2"
memflow-qemu = "0.2"
memflow-win32 = "0.2"

# Async runtime
tokio = { version = "1.0", features = ["full"] }
async-trait = "0.1"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Logging
log = "0.4"
env_logger = "0.10"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Math and utilities
nalgebra = "0.32"
rand = "0.8"

# GUI (optional)
egui = { version = "0.24", optional = true }
eframe = { version = "0.24", optional = true }

# System utilities
sysinfo = "0.29"
procfs = "0.16"
libc = "0.2"
lru = "0.12"

[features]
default = ["gui"]
gui = ["egui", "eframe"]

[[bin]]
name = "cs2-dma"
path = "src/main.rs"

[[bin]]
name = "cs2-radar"
path = "src/radar.rs"
required-features = ["gui"]

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
