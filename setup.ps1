# CS2 Virtual DMA Environment Setup Script for Windows Development
# 用于在Windows环境下准备开发环境

param(
    [switch]$InstallRust,
    [switch]$InstallTools,
    [switch]$SetupWSL
)

Write-Host "=== CS2 Virtual DMA 开发环境配置 ===" -ForegroundColor Green
Write-Host "正在配置Windows开发环境..." -ForegroundColor Yellow

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请以管理员身份运行PowerShell" -ForegroundColor Red
    exit 1
}

# 1. 安装Chocolatey包管理器
if ($InstallTools) {
    Write-Host "1. 安装Chocolatey包管理器..." -ForegroundColor Cyan
    if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    } else {
        Write-Host "Chocolatey已安装，跳过..." -ForegroundColor Green
    }
}

# 2. 安装开发工具
if ($InstallTools) {
    Write-Host "2. 安装开发工具..." -ForegroundColor Cyan
    
    # 安装Git
    if (!(Get-Command git -ErrorAction SilentlyContinue)) {
        choco install git -y
    }
    
    # 安装Visual Studio Code
    if (!(Get-Command code -ErrorAction SilentlyContinue)) {
        choco install vscode -y
    }
    
    # 安装Windows Terminal
    choco install microsoft-windows-terminal -y
    
    # 安装7zip
    choco install 7zip -y
}

# 3. 安装Rust工具链
if ($InstallRust) {
    Write-Host "3. 安装Rust工具链..." -ForegroundColor Cyan
    if (!(Get-Command rustc -ErrorAction SilentlyContinue)) {
        # 下载并安装Rust
        $rustupUrl = "https://win.rustup.rs/x86_64"
        $rustupPath = "$env:TEMP\rustup-init.exe"
        
        Write-Host "下载Rust安装程序..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $rustupUrl -OutFile $rustupPath
        
        Write-Host "安装Rust..." -ForegroundColor Yellow
        & $rustupPath -y --default-toolchain stable
        
        # 更新环境变量
        $env:PATH += ";$env:USERPROFILE\.cargo\bin"
        
        Remove-Item $rustupPath
    } else {
        Write-Host "Rust已安装，跳过..." -ForegroundColor Green
    }
}

# 4. 启用WSL2 (用于Ubuntu虚拟化环境)
if ($SetupWSL) {
    Write-Host "4. 配置WSL2..." -ForegroundColor Cyan
    
    # 启用WSL功能
    dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
    dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
    
    # 设置WSL2为默认版本
    wsl --set-default-version 2
    
    Write-Host "请重启计算机后安装Ubuntu发行版" -ForegroundColor Yellow
    Write-Host "可以从Microsoft Store安装Ubuntu 22.04 LTS" -ForegroundColor Yellow
}

# 5. 创建项目结构
Write-Host "5. 创建项目结构..." -ForegroundColor Cyan
$projectPath = Get-Location

# 创建必要的目录
$directories = @(
    "src\memory",
    "src\game", 
    "src\anti_detection",
    "src\ui",
    "docs",
    "scripts",
    "config"
)

foreach ($dir in $directories) {
    $fullPath = Join-Path $projectPath $dir
    if (!(Test-Path $fullPath)) {
        New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
        Write-Host "创建目录: $dir" -ForegroundColor Green
    }
}

# 6. 创建开发配置文件
Write-Host "6. 创建开发配置..." -ForegroundColor Cyan

# VS Code配置
$vscodeDir = Join-Path $projectPath ".vscode"
if (!(Test-Path $vscodeDir)) {
    New-Item -ItemType Directory -Path $vscodeDir -Force | Out-Null
}

$settingsJson = @"
{
    "rust-analyzer.cargo.features": ["gui"],
    "rust-analyzer.checkOnSave.command": "clippy",
    "files.associations": {
        "*.rs": "rust"
    },
    "editor.formatOnSave": true,
    "rust-analyzer.rustfmt.rangeFormatting.enable": true
}
"@

$settingsPath = Join-Path $vscodeDir "settings.json"
$settingsJson | Out-File -FilePath $settingsPath -Encoding UTF8

# 创建launch.json用于调试
$launchJson = @"
{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug CS2 DMA",
            "cargo": {
                "args": [
                    "build",
                    "--bin=cs2-dma",
                    "--package=cs2-virtual-dma"
                ],
                "filter": {
                    "name": "cs2-dma",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "`${workspaceFolder}",
            "console": "integratedTerminal"
        }
    ]
}
"@

$launchPath = Join-Path $vscodeDir "launch.json"
$launchJson | Out-File -FilePath $launchPath -Encoding UTF8

Write-Host "=== 配置完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Yellow
Write-Host "1. 如果启用了WSL，请重启计算机" -ForegroundColor White
Write-Host "2. 安装Ubuntu 22.04 LTS到WSL2" -ForegroundColor White
Write-Host "3. 在Ubuntu中运行setup.sh脚本" -ForegroundColor White
Write-Host "4. 配置虚拟机和GPU直通" -ForegroundColor White
Write-Host ""
Write-Host "编译项目:" -ForegroundColor Yellow
Write-Host "  cargo build --release" -ForegroundColor White
Write-Host ""
Write-Host "运行测试:" -ForegroundColor Yellow
Write-Host "  cargo test" -ForegroundColor White

# 显示使用说明
Write-Host ""
Write-Host "脚本参数说明:" -ForegroundColor Cyan
Write-Host "  -InstallRust    安装Rust工具链" -ForegroundColor White
Write-Host "  -InstallTools   安装开发工具" -ForegroundColor White  
Write-Host "  -SetupWSL       配置WSL2环境" -ForegroundColor White
Write-Host ""
Write-Host "示例: .\setup.ps1 -InstallRust -InstallTools -SetupWSL" -ForegroundColor Green
