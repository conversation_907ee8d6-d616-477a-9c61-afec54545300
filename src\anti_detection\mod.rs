pub mod stealth;
pub mod randomization;

pub use stealth::<PERSON><PERSON>thManager;
pub use randomization::RandomizationEngine;

use anyhow::Result;
use std::time::Duration;

/// 反检测配置
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct AntiDetectionConfig {
    /// 是否启用内存访问随机化
    pub enable_memory_randomization: bool,
    /// 是否启用时间随机化
    pub enable_timing_randomization: bool,
    /// 是否启用进程隐藏
    pub enable_process_hiding: bool,
    /// 最小读取间隔 (毫秒)
    pub min_read_interval_ms: u64,
    /// 最大读取间隔 (毫秒)
    pub max_read_interval_ms: u64,
    /// 内存访问模式变化频率
    pub pattern_change_frequency: u32,
}

impl Default for AntiDetectionConfig {
    fn default() -> Self {
        Self {
            enable_memory_randomization: true,
            enable_timing_randomization: true,
            enable_process_hiding: true,
            min_read_interval_ms: 10,
            max_read_interval_ms: 50,
            pattern_change_frequency: 100,
        }
    }
}

/// 检测风险等级
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// 反检测策略
pub trait AntiDetectionStrategy {
    /// 应用反检测措施
    async fn apply(&self, iteration: u64) -> Result<()>;
    
    /// 获取当前风险等级
    fn get_risk_level(&self) -> RiskLevel;
    
    /// 调整策略参数
    fn adjust_parameters(&mut self, risk_level: RiskLevel);
}
