#!/bin/bash

# CS2 Virtual DMA Environment Setup Script
# 用于配置Ubuntu环境以支持虚拟化DMA架构

set -e

echo "=== CS2 Virtual DMA 环境配置脚本 ==="
echo "正在配置Ubuntu环境..."

# 检查是否以root权限运行
if [[ $EUID -ne 0 ]]; then
   echo "此脚本需要root权限运行"
   echo "请使用: sudo ./setup.sh"
   exit 1
fi

# 获取当前用户名（即使在sudo下运行）
REAL_USER=${SUDO_USER:-$USER}
REAL_HOME=$(eval echo ~$REAL_USER)

echo "配置用户: $REAL_USER"
echo "用户目录: $REAL_HOME"

# 1. 更新系统包
echo "1. 更新系统包..."
apt update && apt upgrade -y

# 2. 安装基础依赖
echo "2. 安装基础依赖..."
apt install -y \
    curl \
    wget \
    git \
    build-essential \
    pkg-config \
    libssl-dev \
    cmake \
    clang \
    llvm-dev \
    libclang-dev

# 3. 安装虚拟化相关包
echo "3. 安装虚拟化组件..."
apt install -y \
    qemu-kvm \
    libvirt-daemon-system \
    libvirt-clients \
    bridge-utils \
    virt-manager \
    ovmf \
    qemu-utils

# 4. 配置用户组
echo "4. 配置用户组权限..."
usermod -aG libvirt $REAL_USER
usermod -aG kvm $REAL_USER

# 5. 启用虚拟化服务
echo "5. 启用虚拟化服务..."
systemctl enable libvirtd
systemctl start libvirtd

# 6. 安装Rust工具链 (作为普通用户)
echo "6. 安装Rust工具链..."
if ! command -v rustc &> /dev/null; then
    sudo -u $REAL_USER bash -c "curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y"
    sudo -u $REAL_USER bash -c "source $REAL_HOME/.cargo/env && rustup default stable"
else
    echo "Rust已安装，跳过..."
fi

# 7. 安装MemFlow依赖
echo "7. 配置MemFlow依赖..."

# 创建MemFlow配置目录
mkdir -p /etc/memflow
chown $REAL_USER:$REAL_USER /etc/memflow

# 8. 配置内核模块
echo "8. 配置内核模块..."

# 加载必要的内核模块
modprobe kvm
modprobe kvm_intel  # 或 kvm_amd，根据CPU类型

# 确保模块在启动时加载
echo "kvm" >> /etc/modules
echo "kvm_intel" >> /etc/modules  # 或 kvm_amd

# 9. 配置IOMMU (用于GPU直通)
echo "9. 配置IOMMU..."

# 检查GRUB配置
GRUB_FILE="/etc/default/grub"
if ! grep -q "intel_iommu=on" $GRUB_FILE; then
    sed -i 's/GRUB_CMDLINE_LINUX_DEFAULT="/GRUB_CMDLINE_LINUX_DEFAULT="intel_iommu=on iommu=pt /' $GRUB_FILE
    echo "已添加IOMMU配置到GRUB，需要重启生效"
fi

# 10. 创建项目工作目录
echo "10. 创建项目目录..."
PROJECT_DIR="$REAL_HOME/cs2-virtual-dma"
if [ ! -d "$PROJECT_DIR" ]; then
    sudo -u $REAL_USER mkdir -p "$PROJECT_DIR"
fi

# 11. 配置共享内存
echo "11. 配置共享内存..."
echo "tmpfs /dev/shm tmpfs defaults,size=1G 0 0" >> /etc/fstab

# 12. 安装额外的开发工具
echo "12. 安装开发工具..."
apt install -y \
    gdb \
    valgrind \
    strace \
    htop \
    iotop

# 13. 创建虚拟机配置模板
echo "13. 创建虚拟机配置模板..."
cat > /tmp/vm-template.xml << 'EOF'
<domain type='kvm'>
  <name>cs2-test-vm</name>
  <memory unit='KiB'>8388608</memory>
  <vcpu placement='static'>4</vcpu>
  <features>
    <acpi/>
    <apic/>
    <hyperv>
      <relaxed state='on'/>
      <vapic state='on'/>
      <spinlocks state='on' retries='8191'/>
      <vendor_id state='on' value='1234567890ab'/>
    </hyperv>
    <kvm>
      <hidden state='on'/>
    </kvm>
  </features>
  <cpu mode='host-passthrough' check='none'>
    <topology sockets='1' cores='4' threads='1'/>
  </cpu>
  <clock offset='localtime'>
    <timer name='rtc' tickpolicy='catchup'/>
    <timer name='pit' tickpolicy='delay'/>
    <timer name='hpet' present='no'/>
    <timer name='hypervclock' present='yes'/>
  </clock>
</domain>
EOF

chown $REAL_USER:$REAL_USER /tmp/vm-template.xml

echo "=== 环境配置完成 ==="
echo ""
echo "下一步操作："
echo "1. 重启系统以使IOMMU配置生效"
echo "2. 配置GPU直通 (如需要)"
echo "3. 创建Windows 10虚拟机"
echo "4. 编译并运行CS2 Virtual DMA"
echo ""
echo "编译命令:"
echo "  cd $PROJECT_DIR"
echo "  cargo build --release"
echo ""
echo "运行命令:"
echo "  sudo ./target/release/cs2-dma"
echo ""
echo "注意: 请确保虚拟机已正确配置并运行CS2游戏"
