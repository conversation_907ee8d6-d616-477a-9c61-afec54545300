use eframe::egui;
use crate::game::{PlayerEntity, Vector3, Team};
use super::{UITheme, UIUtils};

/// 雷达UI组件
pub struct RadarUI {
    theme: UITheme,
    /// 雷达中心位置
    center_offset: Vector3,
    /// 是否显示网格
    show_grid: bool,
    /// 是否显示距离圈
    show_distance_rings: bool,
    /// 网格间距
    grid_spacing: f32,
}

impl RadarUI {
    pub fn new() -> Self {
        Self {
            theme: UITheme::default(),
            center_offset: Vector3::zero(),
            show_grid: true,
            show_distance_rings: true,
            grid_spacing: 500.0,
        }
    }
    
    /// 绘制雷达
    pub fn draw_radar(
        &self,
        ui: &mut egui::Ui,
        players: &[PlayerEntity],
        config: &super::super::RadarConfig,
    ) -> egui::Response {
        let available_size = ui.available_size();
        let radar_size = egui::Vec2::new(
            available_size.x.min(600.0),
            available_size.y.min(600.0),
        );
        
        let (response, painter) = ui.allocate_painter(radar_size, egui::Sense::hover());
        let radar_rect = response.rect;
        let center = radar_rect.center();
        let radius = radar_rect.width().min(radar_rect.height()) / 2.0 - 10.0;
        
        // 绘制雷达背景
        painter.rect_filled(
            radar_rect,
            egui::Rounding::same(8.0),
            self.theme.background_color,
        );
        
        // 绘制边框
        painter.rect_stroke(
            radar_rect,
            egui::Rounding::same(8.0),
            egui::Stroke::new(2.0, self.theme.accent_color),
        );
        
        // 绘制网格和距离圈
        if self.show_grid {
            self.draw_grid(&painter, center, radius);
        }
        
        if self.show_distance_rings {
            self.draw_distance_rings(&painter, center, radius, config.max_distance);
        }
        
        // 绘制玩家
        self.draw_players(&painter, center, radius, players, config);
        
        response
    }
    
    /// 绘制网格
    fn draw_grid(&self, painter: &egui::Painter, center: egui::Pos2, radius: f32) {
        let grid_color = egui::Color32::from_rgba_unmultiplied(100, 100, 100, 50);
        
        // 绘制十字线
        painter.line_segment(
            [
                egui::Pos2::new(center.x - radius, center.y),
                egui::Pos2::new(center.x + radius, center.y),
            ],
            egui::Stroke::new(1.0, grid_color),
        );
        
        painter.line_segment(
            [
                egui::Pos2::new(center.x, center.y - radius),
                egui::Pos2::new(center.x, center.y + radius),
            ],
            egui::Stroke::new(1.0, grid_color),
        );
        
        // 绘制网格线
        let grid_step = radius / 4.0;
        for i in 1..4 {
            let offset = grid_step * i as f32;
            
            // 垂直线
            painter.line_segment(
                [
                    egui::Pos2::new(center.x - offset, center.y - radius),
                    egui::Pos2::new(center.x - offset, center.y + radius),
                ],
                egui::Stroke::new(0.5, grid_color),
            );
            
            painter.line_segment(
                [
                    egui::Pos2::new(center.x + offset, center.y - radius),
                    egui::Pos2::new(center.x + offset, center.y + radius),
                ],
                egui::Stroke::new(0.5, grid_color),
            );
            
            // 水平线
            painter.line_segment(
                [
                    egui::Pos2::new(center.x - radius, center.y - offset),
                    egui::Pos2::new(center.x + radius, center.y - offset),
                ],
                egui::Stroke::new(0.5, grid_color),
            );
            
            painter.line_segment(
                [
                    egui::Pos2::new(center.x - radius, center.y + offset),
                    egui::Pos2::new(center.x + radius, center.y + offset),
                ],
                egui::Stroke::new(0.5, grid_color),
            );
        }
    }
    
    /// 绘制距离圈
    fn draw_distance_rings(&self, painter: &egui::Painter, center: egui::Pos2, radius: f32, max_distance: f32) {
        let ring_color = egui::Color32::from_rgba_unmultiplied(150, 150, 150, 80);
        
        for i in 1..=4 {
            let ring_radius = radius * i as f32 / 4.0;
            painter.circle_stroke(
                center,
                ring_radius,
                egui::Stroke::new(1.0, ring_color),
            );
            
            // 绘制距离标签
            let distance = max_distance * i as f32 / 4.0;
            painter.text(
                egui::Pos2::new(center.x + ring_radius - 20.0, center.y - 10.0),
                egui::Align2::CENTER_CENTER,
                format!("{:.0}m", distance),
                egui::FontId::monospace(10.0),
                egui::Color32::LIGHT_GRAY,
            );
        }
    }
    
    /// 绘制玩家
    fn draw_players(
        &self,
        painter: &egui::Painter,
        center: egui::Pos2,
        radius: f32,
        players: &[PlayerEntity],
        config: &super::super::RadarConfig,
    ) {
        // 找到本地玩家
        let local_player = players.iter().find(|p| p.index == 1); // 假设索引1是本地玩家
        let reference_pos = local_player.map(|p| p.position).unwrap_or(Vector3::zero());
        
        // 绘制所有玩家
        for player in players {
            if !player.is_valid() {
                continue;
            }
            
            // 检查是否应该显示此玩家
            let is_local = Some(player) == local_player;
            let is_teammate = local_player.map_or(false, |lp| player.is_teammate(lp.team));
            let is_enemy = local_player.map_or(false, |lp| player.is_enemy(lp.team));
            
            if !is_local && !config.show_teammates && is_teammate {
                continue;
            }
            
            if !config.show_enemies && is_enemy {
                continue;
            }
            
            // 计算屏幕位置
            if let Some(screen_pos) = UIUtils::world_to_screen(
                player.position,
                reference_pos,
                center,
                config.scale,
                config.max_distance,
                radius,
            ) {
                self.draw_single_player(painter, screen_pos, player, is_local);
            }
        }
    }
    
    /// 绘制单个玩家
    fn draw_single_player(
        &self,
        painter: &egui::Painter,
        pos: egui::Pos2,
        player: &PlayerEntity,
        is_local: bool,
    ) {
        // 选择颜色
        let color = if is_local {
            self.theme.local_player_color
        } else {
            match player.team {
                Team::Terrorist => self.theme.enemy_color,
                Team::CounterTerrorist => self.theme.teammate_color,
                _ => egui::Color32::GRAY,
            }
        };
        
        // 绘制玩家点
        let point_radius = if is_local { 8.0 } else { 6.0 };
        painter.circle_filled(pos, point_radius, color);
        
        // 绘制边框
        painter.circle_stroke(
            pos,
            point_radius,
            egui::Stroke::new(1.0, egui::Color32::WHITE),
        );
        
        // 绘制血量条
        if player.health > 0 && player.health < 100 {
            self.draw_health_bar(painter, pos, player.health, point_radius);
        }
        
        // 绘制视角方向（仅本地玩家）
        if is_local {
            let view_angle = player.view_angles.y.to_radians();
            UIUtils::draw_direction_indicator(
                painter,
                pos,
                view_angle,
                point_radius + 5.0,
                egui::Color32::WHITE,
            );
        }
        
        // 绘制玩家名称
        if !player.name.is_empty() {
            painter.text(
                egui::Pos2::new(pos.x, pos.y + point_radius + 12.0),
                egui::Align2::CENTER_TOP,
                &player.name,
                egui::FontId::monospace(9.0),
                self.theme.text_color,
            );
        }
    }
    
    /// 绘制血量条
    fn draw_health_bar(&self, painter: &egui::Painter, pos: egui::Pos2, health: i32, player_radius: f32) {
        let bar_width = 20.0;
        let bar_height = 3.0;
        let bar_y = pos.y - player_radius - 8.0;
        
        let health_ratio = (health as f32 / 100.0).clamp(0.0, 1.0);
        
        // 背景
        painter.rect_filled(
            egui::Rect::from_center_size(
                egui::Pos2::new(pos.x, bar_y),
                egui::Vec2::new(bar_width, bar_height),
            ),
            egui::Rounding::same(1.0),
            egui::Color32::from_rgb(50, 50, 50),
        );
        
        // 血量
        let health_color = if health_ratio > 0.6 {
            egui::Color32::GREEN
        } else if health_ratio > 0.3 {
            egui::Color32::YELLOW
        } else {
            egui::Color32::RED
        };
        
        painter.rect_filled(
            egui::Rect::from_center_size(
                egui::Pos2::new(pos.x - bar_width / 2.0 + (bar_width * health_ratio) / 2.0, bar_y),
                egui::Vec2::new(bar_width * health_ratio, bar_height),
            ),
            egui::Rounding::same(1.0),
            health_color,
        );
    }
    
    /// 设置主题
    pub fn set_theme(&mut self, theme: UITheme) {
        self.theme = theme;
    }
    
    /// 获取主题
    pub fn theme(&self) -> &UITheme {
        &self.theme
    }
}
