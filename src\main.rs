use anyhow::Result;
use log::{info, warn, error};
use std::time::Duration;
use tokio::time::sleep;
use std::sync::Arc;

mod memory;
mod game;
mod anti_detection;

use memory::MemoryConnector;
use game::{GameProcess, EntityManager};
use anti_detection::StealthManager;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志系统
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();

    info!("CS2 Virtual DMA - Red Team Testing Framework");
    info!("正在启动虚拟化DMA引擎...");

    // 检查运行权限
    if !is_running_as_root() {
        error!("需要root权限来访问虚拟机内存");
        return Err(anyhow::anyhow!("需要root权限"));
    }

    // 初始化隐蔽管理器
    let stealth_manager = StealthManager::new();
    stealth_manager.initialize_stealth_mode().await?;

    // 初始化内存连接器
    let memory_connector = Arc::new(MemoryConnector::new().await?);
    info!("内存连接器初始化成功");

    // 发现游戏进程
    let mut game_process = GameProcess::new();
    
    loop {
        match game_process.find_cs2_process(&memory_connector).await {
            Ok(process_info) => {
                info!("发现CS2进程: PID={}, 基址=0x{:x}",
                      process_info.pid, process_info.base_address);

                // 初始化实体管理器
                let mut entity_manager = EntityManager::with_connector(process_info, memory_connector.clone());

                // 主数据提取循环
                run_data_extraction_loop(&memory_connector, &mut entity_manager, &stealth_manager).await?;
                
                break;
            }
            Err(e) => {
                warn!("未找到CS2进程: {}", e);
                sleep(Duration::from_secs(5)).await;
            }
        }
    }

    Ok(())
}

async fn run_data_extraction_loop(
    memory_connector: &MemoryConnector,
    entity_manager: &mut EntityManager,
    stealth_manager: &StealthManager,
) -> Result<()> {
    info!("开始数据提取循环");
    
    let mut iteration_count = 0u64;
    
    loop {
        // 应用反检测随机化
        stealth_manager.apply_randomization(iteration_count).await?;
        
        // 更新实体数据
        match entity_manager.update_entities(memory_connector).await {
            Ok(entities) => {
                // 输出实体信息用于测试
                for (i, entity) in entities.iter().enumerate() {
                    if entity.is_valid() {
                        info!("玩家 {}: 位置=({:.2}, {:.2}, {:.2}), 血量={}, 队伍={}", 
                              i, entity.position.x, entity.position.y, entity.position.z,
                              entity.health, entity.team);
                    }
                }
                
                // 将数据写入共享内存供雷达界面使用
                write_entities_to_shared_memory(&entities).await?;
            }
            Err(e) => {
                warn!("实体更新失败: {}", e);
            }
        }
        
        iteration_count += 1;
        
        // 动态调整更新频率以避免检测
        let sleep_duration = stealth_manager.get_dynamic_sleep_duration(iteration_count);
        sleep(sleep_duration).await;
    }
}

async fn write_entities_to_shared_memory(entities: &[game::PlayerEntity]) -> Result<()> {
    // 这里实现将实体数据写入共享内存的逻辑
    // 供雷达界面程序读取
    Ok(())
}

fn is_running_as_root() -> bool {
    unsafe { libc::geteuid() == 0 }
}
