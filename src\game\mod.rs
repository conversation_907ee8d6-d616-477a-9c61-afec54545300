pub mod process;
pub mod entities;
pub mod offsets;

pub use process::GameProcess;
pub use entities::{PlayerEntity, EntityManager, Vector3, Team};
pub use offsets::CS2Offsets;

use anyhow::Result;
use serde::{Serialize, Deserialize};

/// 游戏状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GameState {
    Menu,
    Loading,
    InGame,
    Spectating,
    Unknown,
}

/// 游戏信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GameInfo {
    pub state: GameState,
    pub map_name: String,
    pub player_count: u32,
    pub max_players: u32,
    pub round_number: u32,
    pub time_remaining: f32,
}

impl Default for GameInfo {
    fn default() -> Self {
        Self {
            state: GameState::Unknown,
            map_name: String::new(),
            player_count: 0,
            max_players: 0,
            round_number: 0,
            time_remaining: 0.0,
        }
    }
}

/// 游戏配置
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GameConfig {
    /// 最大玩家数量
    pub max_players: usize,
    /// 更新频率 (毫秒)
    pub update_interval_ms: u64,
    /// 是否启用ESP
    pub enable_esp: bool,
    /// ESP最大距离
    pub esp_max_distance: f32,
    /// 是否显示队友
    pub show_teammates: bool,
    /// 是否显示敌人
    pub show_enemies: bool,
}

impl Default for GameConfig {
    fn default() -> Self {
        Self {
            max_players: 64,
            update_interval_ms: 16, // ~60 FPS
            enable_esp: true,
            esp_max_distance: 1000.0,
            show_teammates: true,
            show_enemies: true,
        }
    }
}
