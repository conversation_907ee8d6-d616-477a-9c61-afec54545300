use anyhow::{Result, Context};
use log::{debug, warn};
use std::sync::Arc;
use tokio::sync::RwLock;

use super::{MemoryAccess, MemoryConnector};

/// 高级内存读取器，提供便捷的内存访问方法
pub struct MemoryReader {
    connector: Arc<MemoryConnector>,
    /// 读取缓存，用于提高性能
    cache: Arc<RwLock<lru::LruCache<u64, Vec<u8>>>>,
}

impl MemoryReader {
    /// 创建新的内存读取器
    pub fn new(connector: Arc<MemoryConnector>) -> Self {
        Self {
            connector,
            cache: Arc::new(RwLock::new(lru::LruCache::new(1000))),
        }
    }
    
    /// 读取指针链
    pub async fn read_pointer_chain(&self, base_address: u64, offsets: &[u64]) -> Result<u64> {
        let mut current_address = base_address;
        
        for (i, &offset) in offsets.iter().enumerate() {
            if i == offsets.len() - 1 {
                // 最后一个偏移量，直接加上
                current_address += offset;
            } else {
                // 读取指针值
                current_address = self.read_u64(current_address).await?;
                current_address += offset;
            }
            
            debug!("指针链步骤 {}: 0x{:x}", i, current_address);
        }
        
        Ok(current_address)
    }
    
    /// 读取字符串
    pub async fn read_string(&self, address: u64, max_length: usize) -> Result<String> {
        let bytes = self.connector.read_bytes(address, max_length).await?;
        
        // 查找字符串结束符
        let end_pos = bytes.iter().position(|&b| b == 0).unwrap_or(bytes.len());
        let string_bytes = &bytes[..end_pos];
        
        String::from_utf8(string_bytes.to_vec())
            .context("无效的UTF-8字符串")
    }
    
    /// 读取宽字符串 (UTF-16)
    pub async fn read_wide_string(&self, address: u64, max_length: usize) -> Result<String> {
        let byte_length = max_length * 2; // UTF-16每个字符2字节
        let bytes = self.connector.read_bytes(address, byte_length).await?;
        
        // 转换为u16数组
        let mut wide_chars = Vec::new();
        for chunk in bytes.chunks_exact(2) {
            let wide_char = u16::from_le_bytes([chunk[0], chunk[1]]);
            if wide_char == 0 {
                break;
            }
            wide_chars.push(wide_char);
        }
        
        String::from_utf16(&wide_chars)
            .context("无效的UTF-16字符串")
    }
    
    /// 批量读取结构体数组
    pub async fn read_struct_array<T>(&self, base_address: u64, count: usize, stride: usize) -> Result<Vec<T>>
    where
        T: memflow::prelude::Pod + Clone,
    {
        let mut results = Vec::with_capacity(count);
        let struct_size = std::mem::size_of::<T>();
        
        for i in 0..count {
            let address = base_address + (i * stride) as u64;
            match self.connector.read::<T>(address).await {
                Ok(data) => results.push(data),
                Err(e) => {
                    warn!("读取结构体数组索引 {} 失败: {}", i, e);
                    break;
                }
            }
        }
        
        Ok(results)
    }
    
    /// 搜索内存模式
    pub async fn search_pattern(&self, start_address: u64, size: usize, pattern: &[u8], mask: &[u8]) -> Result<Vec<u64>> {
        let memory_data = self.connector.read_bytes(start_address, size).await?;
        let mut matches = Vec::new();
        
        if pattern.len() != mask.len() {
            return Err(anyhow::anyhow!("模式和掩码长度不匹配"));
        }
        
        for i in 0..=memory_data.len().saturating_sub(pattern.len()) {
            let mut is_match = true;
            
            for j in 0..pattern.len() {
                if mask[j] != 0 && memory_data[i + j] != pattern[j] {
                    is_match = false;
                    break;
                }
            }
            
            if is_match {
                matches.push(start_address + i as u64);
            }
        }
        
        debug!("在地址 0x{:x} 找到 {} 个模式匹配", start_address, matches.len());
        Ok(matches)
    }
    
    /// 验证地址有效性
    pub async fn is_valid_address(&self, address: u64) -> bool {
        // 检查地址是否在有效范围内
        if address < 0x10000 || address > 0x7FFFFFFFFFFF {
            return false;
        }
        
        // 尝试读取一个字节来验证
        self.connector.is_readable(address, 1).await
    }
}

// 便捷的类型读取方法
impl MemoryReader {
    pub async fn read_u8(&self, address: u64) -> Result<u8> {
        self.connector.read::<u8>(address).await
    }
    
    pub async fn read_u16(&self, address: u64) -> Result<u16> {
        self.connector.read::<u16>(address).await
    }
    
    pub async fn read_u32(&self, address: u64) -> Result<u32> {
        self.connector.read::<u32>(address).await
    }
    
    pub async fn read_u64(&self, address: u64) -> Result<u64> {
        self.connector.read::<u64>(address).await
    }
    
    pub async fn read_i8(&self, address: u64) -> Result<i8> {
        self.connector.read::<i8>(address).await
    }
    
    pub async fn read_i16(&self, address: u64) -> Result<i16> {
        self.connector.read::<i16>(address).await
    }
    
    pub async fn read_i32(&self, address: u64) -> Result<i32> {
        self.connector.read::<i32>(address).await
    }
    
    pub async fn read_i64(&self, address: u64) -> Result<i64> {
        self.connector.read::<i64>(address).await
    }
    
    pub async fn read_f32(&self, address: u64) -> Result<f32> {
        self.connector.read::<f32>(address).await
    }
    
    pub async fn read_f64(&self, address: u64) -> Result<f64> {
        self.connector.read::<f64>(address).await
    }
}
