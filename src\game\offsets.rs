/// CS2内存偏移量定义
/// 注意: 这些偏移量会随着游戏更新而变化，需要定期更新
#[derive(Debug, Clone)]
pub struct CS2Offsets {
    // 客户端偏移量
    pub dwLocalPlayerPawn: u64,
    pub dwEntityList: u64,
    pub dwViewMatrix: u64,
    pub dwViewAngles: u64,
    pub dwLocalPlayerController: u64,
    
    // 实体偏移量
    pub m_iHealth: u64,
    pub m_ArmorValue: u64,
    pub m_iTeamNum: u64,
    pub m_vOldOrigin: u64,
    pub m_vecViewOffset: u64,
    pub m_vecVelocity: u64,
    pub m_angEyeAngles: u64,
    pub m_bDormant: u64,
    pub m_lifeState: u64,
    pub m_fFlags: u64,
    pub m_iIDEntIndex: u64,
    pub m_hPlayerPawn: u64,
    pub m_iszPlayerName: u64,
    
    // 武器偏移量
    pub m_pClippingWeapon: u64,
    pub m_AttributeManager: u64,
    pub m_Item: u64,
    pub m_iItemDefinitionIndex: u64,
    
    // 游戏规则偏移量
    pub m_pGameRules: u64,
    pub m_bWarmupPeriod: u64,
    pub m_iRoundNum: u64,
    pub m_fRoundStartTime: u64,
    pub m_flRestartRoundTime: u64,
    
    // 引擎偏移量
    pub dwForceJump: u64,
    pub dwForceAttack: u64,
    pub dwForceAttack2: u64,
    pub dwForceLeft: u64,
    pub dwForceRight: u64,
    pub dwForceForward: u64,
    pub dwForceBack: u64,
}

impl CS2Offsets {
    /// 创建默认的CS2偏移量
    /// 注意: 这些是示例偏移量，实际使用时需要根据当前游戏版本更新
    pub fn new() -> Self {
        Self {
            // 客户端偏移量 (需要根据实际版本更新)
            dwLocalPlayerPawn: 0x17371C8,
            dwEntityList: 0x18C2D58,
            dwViewMatrix: 0x1913D40,
            dwViewAngles: 0x1919D10,
            dwLocalPlayerController: 0x1906118,
            
            // 实体偏移量
            m_iHealth: 0x32C,
            m_ArmorValue: 0x2340,
            m_iTeamNum: 0x3BF,
            m_vOldOrigin: 0x1274,
            m_vecViewOffset: 0xC58,
            m_vecVelocity: 0x1280,
            m_angEyeAngles: 0x1578,
            m_bDormant: 0xE7,
            m_lifeState: 0x338,
            m_fFlags: 0x3EC,
            m_iIDEntIndex: 0x1538,
            m_hPlayerPawn: 0x80C,
            m_iszPlayerName: 0x640,
            
            // 武器偏移量
            m_pClippingWeapon: 0x12A8,
            m_AttributeManager: 0x1060,
            m_Item: 0x50,
            m_iItemDefinitionIndex: 0x1FA,
            
            // 游戏规则偏移量
            m_pGameRules: 0x0, // 需要动态获取
            m_bWarmupPeriod: 0x125,
            m_iRoundNum: 0x124,
            m_fRoundStartTime: 0x4C,
            m_flRestartRoundTime: 0x50,
            
            // 引擎偏移量
            dwForceJump: 0x1713CC0,
            dwForceAttack: 0x1713B50,
            dwForceAttack2: 0x1713BE0,
            dwForceLeft: 0x1713C90,
            dwForceRight: 0x1713C60,
            dwForceForward: 0x1713C30,
            dwForceBack: 0x1713C00,
        }
    }
    
    /// 从配置文件加载偏移量
    pub fn from_config(config_path: &str) -> anyhow::Result<Self> {
        use std::fs;
        
        let config_content = fs::read_to_string(config_path)?;
        let offsets: CS2Offsets = serde_json::from_str(&config_content)?;
        
        Ok(offsets)
    }
    
    /// 保存偏移量到配置文件
    pub fn save_to_config(&self, config_path: &str) -> anyhow::Result<()> {
        use std::fs;
        
        let config_content = serde_json::to_string_pretty(self)?;
        fs::write(config_path, config_content)?;
        
        Ok(())
    }
    
    /// 验证偏移量是否有效
    pub fn validate(&self) -> bool {
        // 基本的偏移量验证
        self.dwLocalPlayerPawn > 0 &&
        self.dwEntityList > 0 &&
        self.dwViewMatrix > 0 &&
        self.m_iHealth > 0 &&
        self.m_vOldOrigin > 0
    }
    
    /// 更新偏移量（从在线源或签名扫描）
    pub async fn update_offsets(&mut self) -> anyhow::Result<()> {
        // 这里可以实现从在线源更新偏移量的逻辑
        // 或者使用签名扫描来动态查找偏移量
        
        log::info!("正在更新CS2偏移量...");
        
        // 示例: 从GitHub或其他源获取最新偏移量
        // let response = reqwest::get("https://api.example.com/cs2-offsets").await?;
        // let new_offsets: CS2Offsets = response.json().await?;
        // *self = new_offsets;
        
        log::info!("偏移量更新完成");
        Ok(())
    }
}

impl Default for CS2Offsets {
    fn default() -> Self {
        Self::new()
    }
}

// 为了支持序列化，需要实现Serialize和Deserialize
use serde::{Serialize, Deserialize};

impl Serialize for CS2Offsets {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        use serde::ser::SerializeStruct;
        
        let mut state = serializer.serialize_struct("CS2Offsets", 30)?;
        
        // 序列化所有字段
        state.serialize_field("dwLocalPlayerPawn", &format!("0x{:X}", self.dwLocalPlayerPawn))?;
        state.serialize_field("dwEntityList", &format!("0x{:X}", self.dwEntityList))?;
        state.serialize_field("dwViewMatrix", &format!("0x{:X}", self.dwViewMatrix))?;
        state.serialize_field("dwViewAngles", &format!("0x{:X}", self.dwViewAngles))?;
        state.serialize_field("dwLocalPlayerController", &format!("0x{:X}", self.dwLocalPlayerController))?;
        
        state.serialize_field("m_iHealth", &format!("0x{:X}", self.m_iHealth))?;
        state.serialize_field("m_ArmorValue", &format!("0x{:X}", self.m_ArmorValue))?;
        state.serialize_field("m_iTeamNum", &format!("0x{:X}", self.m_iTeamNum))?;
        state.serialize_field("m_vOldOrigin", &format!("0x{:X}", self.m_vOldOrigin))?;
        state.serialize_field("m_vecViewOffset", &format!("0x{:X}", self.m_vecViewOffset))?;
        
        // ... 其他字段
        
        state.end()
    }
}

/// 偏移量管理器
pub struct OffsetManager {
    offsets: CS2Offsets,
    last_update: std::time::Instant,
    update_interval: std::time::Duration,
}

impl OffsetManager {
    pub fn new() -> Self {
        Self {
            offsets: CS2Offsets::new(),
            last_update: std::time::Instant::now(),
            update_interval: std::time::Duration::from_secs(3600), // 1小时更新一次
        }
    }
    
    pub fn get_offsets(&self) -> &CS2Offsets {
        &self.offsets
    }
    
    pub async fn check_and_update(&mut self) -> anyhow::Result<bool> {
        if self.last_update.elapsed() >= self.update_interval {
            self.offsets.update_offsets().await?;
            self.last_update = std::time::Instant::now();
            Ok(true)
        } else {
            Ok(false)
        }
    }
}
