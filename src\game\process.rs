use anyhow::{Result, Context};
use log::{info, warn, debug, error};
use std::time::{Duration, Instant};
use tokio::time::sleep;

use crate::memory::{MemoryConnector, ProcessInfo, MemoryRegion};

/// CS2游戏进程管理器
pub struct GameProcess {
    /// 当前进程信息
    process_info: Option<ProcessInfo>,
    /// 模块基址缓存
    module_cache: std::collections::HashMap<String, u64>,
    /// 上次扫描时间
    last_scan_time: Option<Instant>,
    /// 扫描间隔
    scan_interval: Duration,
}

impl GameProcess {
    /// 创建新的游戏进程管理器
    pub fn new() -> Self {
        Self {
            process_info: None,
            module_cache: std::collections::HashMap::new(),
            last_scan_time: None,
            scan_interval: Duration::from_secs(5),
        }
    }
    
    /// 查找CS2游戏进程
    pub async fn find_cs2_process(&mut self, connector: &MemoryConnector) -> Result<ProcessInfo> {
        // 检查是否需要重新扫描
        if let Some(last_scan) = self.last_scan_time {
            if last_scan.elapsed() < self.scan_interval {
                if let Some(ref process) = self.process_info {
                    return Ok(process.clone());
                }
            }
        }
        
        info!("开始扫描CS2进程...");
        
        // 扫描所有进程
        let processes = connector.scan_processes().await?;
        
        // CS2可能的进程名称
        let cs2_process_names = [
            "cs2.exe",
            "csgo.exe", 
            "Counter-Strike 2.exe",
            "steam.exe", // 有时CS2通过Steam启动
        ];
        
        let mut candidates = Vec::new();
        
        for process in processes {
            let process_name_lower = process.name.to_lowercase();
            
            for &target_name in &cs2_process_names {
                if process_name_lower.contains(&target_name.to_lowercase()) {
                    candidates.push(process);
                    break;
                }
            }
        }
        
        // 如果找到多个候选进程，选择最合适的
        let selected_process = if candidates.is_empty() {
            return Err(anyhow::anyhow!("未找到CS2进程"));
        } else if candidates.len() == 1 {
            candidates.into_iter().next().unwrap()
        } else {
            // 选择内存占用最大的进程（通常是游戏主进程）
            candidates.into_iter()
                .max_by_key(|p| p.memory_size)
                .unwrap()
        };
        
        info!("找到CS2进程: {} (PID: {})", selected_process.name, selected_process.pid);
        
        // 附加到进程
        let process_info = connector.attach_to_process(selected_process.pid).await?;
        
        // 缓存进程信息
        self.process_info = Some(process_info.clone());
        self.last_scan_time = Some(Instant::now());
        
        // 扫描模块
        self.scan_modules(connector).await?;
        
        Ok(process_info)
    }
    
    /// 扫描进程模块
    async fn scan_modules(&mut self, connector: &MemoryConnector) -> Result<()> {
        debug!("扫描进程模块...");
        
        let regions = connector.get_memory_regions().await?;
        self.module_cache.clear();
        
        for region in regions {
            // 只缓存重要的模块
            let module_name_lower = region.name.to_lowercase();
            
            if module_name_lower.contains("client.dll") ||
               module_name_lower.contains("engine2.dll") ||
               module_name_lower.contains("server.dll") ||
               module_name_lower.contains("cs2.exe") ||
               module_name_lower.contains("csgo.exe") {
                
                self.module_cache.insert(region.name.clone(), region.start_address);
                info!("缓存模块: {} -> 0x{:x}", region.name, region.start_address);
            }
        }
        
        Ok(())
    }
    
    /// 获取模块基址
    pub fn get_module_base(&self, module_name: &str) -> Option<u64> {
        // 尝试精确匹配
        if let Some(&base_addr) = self.module_cache.get(module_name) {
            return Some(base_addr);
        }
        
        // 尝试模糊匹配
        let module_name_lower = module_name.to_lowercase();
        for (cached_name, &base_addr) in &self.module_cache {
            if cached_name.to_lowercase().contains(&module_name_lower) {
                return Some(base_addr);
            }
        }
        
        None
    }
    
    /// 获取客户端模块基址
    pub fn get_client_base(&self) -> Option<u64> {
        self.get_module_base("client.dll")
            .or_else(|| self.get_module_base("client"))
    }
    
    /// 获取引擎模块基址
    pub fn get_engine_base(&self) -> Option<u64> {
        self.get_module_base("engine2.dll")
            .or_else(|| self.get_module_base("engine"))
    }
    
    /// 获取服务器模块基址
    pub fn get_server_base(&self) -> Option<u64> {
        self.get_module_base("server.dll")
            .or_else(|| self.get_module_base("server"))
    }
    
    /// 检查进程是否仍然有效
    pub async fn is_process_valid(&self, connector: &MemoryConnector) -> bool {
        if let Some(ref process) = self.process_info {
            // 尝试读取进程内存来验证
            connector.is_readable(process.base_address, 4).await
        } else {
            false
        }
    }
    
    /// 获取当前进程信息
    pub fn get_process_info(&self) -> Option<&ProcessInfo> {
        self.process_info.as_ref()
    }
    
    /// 等待CS2进程启动
    pub async fn wait_for_cs2_process(&mut self, connector: &MemoryConnector, timeout: Duration) -> Result<ProcessInfo> {
        let start_time = Instant::now();
        
        info!("等待CS2进程启动，超时时间: {:?}", timeout);
        
        loop {
            match self.find_cs2_process(connector).await {
                Ok(process_info) => {
                    info!("CS2进程已启动: {} (PID: {})", process_info.name, process_info.pid);
                    return Ok(process_info);
                }
                Err(e) => {
                    if start_time.elapsed() >= timeout {
                        return Err(anyhow::anyhow!("等待CS2进程超时: {}", e));
                    }
                    
                    debug!("CS2进程未找到，继续等待...");
                    sleep(Duration::from_secs(2)).await;
                }
            }
        }
    }
}
