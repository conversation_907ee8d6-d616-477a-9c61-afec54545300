# CS2 Virtual DMA - Red Team Testing Framework

## 项目概述

这是一个基于虚拟化技术的DMA架构，专门用于CS2反作弊系统的红蓝对抗测试。通过MemFlow-QEMU技术实现从Ubuntu宿主机访问Win10虚拟机内存，模拟高级作弊行为以测试反作弊系统的有效性。

## 技术架构

```
Ubuntu Host (宿主机)
├── MemFlow Framework
├── QEMU/KVM 虚拟机管理
├── 内存访问引擎
└── 雷达显示界面

Win10 VM (虚拟机)
├── CS2 游戏进程
├── GPU直通 (NVIDIA)
└── 去虚拟化配置
```

## 核心特性

- **虚拟化DMA**: 无需物理DMA设备，通过虚拟化层访问内存
- **反检测机制**: 多层反检测技术，规避传统检测方法
- **实时数据提取**: 高效提取玩家位置、血量等关键数据
- **可视化界面**: 实时雷达显示，便于测试验证

## 环境要求

### 硬件要求
- CPU: 支持虚拟化技术 (Intel VT-x / AMD-V)
- GPU: NVIDIA显卡 (用于GPU直通)
- 内存: 至少16GB RAM
- 存储: 至少100GB可用空间

### 软件要求
- Ubuntu 22.04 LTS (宿主机)
- QEMU/KVM 虚拟化平台
- Rust 1.70+ 工具链
- Windows 10 (虚拟机)

## 快速开始

### 1. 环境配置

```bash
# 安装基础依赖
sudo apt update
sudo apt install qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils

# 安装Rust工具链
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# 克隆项目
git clone <repository-url>
cd cs2-virtual-dma
```

### 2. 编译项目

```bash
# 编译主程序
cargo build --release

# 编译雷达界面
cargo build --release --bin cs2-radar
```

### 3. 运行测试

```bash
# 启动内存访问引擎
sudo ./target/release/cs2-dma

# 启动雷达界面 (另一个终端)
./target/release/cs2-radar
```

## 项目结构

```
src/
├── main.rs              # 主程序入口
├── radar.rs             # 雷达界面程序
├── memory/              # 内存访问模块
│   ├── mod.rs
│   ├── connector.rs     # MemFlow连接器
│   └── reader.rs        # 内存读取器
├── game/                # 游戏相关模块
│   ├── mod.rs
│   ├── process.rs       # 进程发现
│   ├── entities.rs      # 实体数据结构
│   └── offsets.rs       # 内存偏移量
├── anti_detection/      # 反检测模块
│   ├── mod.rs
│   ├── stealth.rs       # 隐蔽技术
│   └── randomization.rs # 随机化技术
└── ui/                  # 用户界面
    ├── mod.rs
    └── radar.rs         # 雷达显示
```

## 安全说明

⚠️ **重要提醒**: 此项目仅用于授权的安全测试和反作弊系统改进。严禁用于实际游戏作弊或其他非法用途。

## 许可证

本项目仅供内部安全研究使用，未经授权不得分发或商业使用。
